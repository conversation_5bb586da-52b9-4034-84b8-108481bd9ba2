import { useState, useCallback } from 'react';
import {
  useTemplatesApi,
  Template,
  CreateTemplateRequest,
  UpdateTemplateRequest,
} from 'src/services/api/templates-api';
import { TemplateFormValues } from '../form/template-schema';

// Helper functions to convert between form data and API data
const convertFormToApiRequest = (data: TemplateFormValues): CreateTemplateRequest => {
  return {
    name: data.name,
    description: data.description,
    systemMessage: data.systemMessage,
    type: data.type,
    categoryId: data.categoryId,
    model: data.model,
    toolsId: data.toolsId,
  };
};

const convertFormToUpdateRequest = (data: TemplateFormValues): UpdateTemplateRequest => {
  return {
    name: data.name,
    description: data.description,
    systemMessage: data.systemMessage,
    type: data.type,
    categoryId: data.categoryId,
    model: data.model,
    toolsId: data.toolsId,
  };
};

export const useTemplatesView = () => {
  // State management
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Get the API hooks
  const { useGetTemplates, useCreateTemplate, useUpdateTemplate, useDeleteTemplate } =
    useTemplatesApi();

  // API hooks with proper loading states and refetch
  const { mutate: createTemplate } = useCreateTemplate();
  const { mutate: updateTemplate } = useUpdateTemplate(selectedTemplate?.id || 0);
  const { mutate: deleteTemplate } = useDeleteTemplate();

  // Get templates data from the API
  const { data: templatesResponse, isLoading, isError, refetch } = useGetTemplates();

  // Extract templates and handle loading/error states
  const templates = templatesResponse?.categories || [];
  const loading = isLoading;
  const error = isError ? 'Failed to load templates' : null;

  // Refetch templates data
  const fetchTemplates = useCallback(() => {
    refetch();
  }, [refetch]);

  // Dialog handlers
  const handleOpenDialog = useCallback(() => {
    setOpenDialog(true);
  }, []);

  const handleCloseDialog = useCallback(() => {
    setOpenDialog(false);
    setSelectedTemplate(null);
  }, []);

  const handleOpenCreateDialog = useCallback(() => {
    setSelectedTemplate(null);
    handleOpenDialog();
  }, [handleOpenDialog]);

  // Handle creating a new template
  const handleCreateTemplate = useCallback(
    (data: TemplateFormValues) => {
      // Set loading state
      setIsCreating(true);

      // Convert form data to API request format
      const apiRequest = convertFormToApiRequest(data);

      // Use the mutation from the API hook
      createTemplate(apiRequest, {
        onSuccess: () => {
          // On success: refetch data from backend and close dialog
          refetch();
          handleCloseDialog();
          setIsCreating(false);
        },
        onError: (err) => {
          console.error('Failed to create template:', err);
          setIsCreating(false);
        },
      });
    },
    [createTemplate, handleCloseDialog, refetch]
  );

  // Handle updating a template
  const handleUpdateTemplate = useCallback(
    (data: TemplateFormValues) => {
      if (!selectedTemplate) return;

      // Set loading state
      setIsUpdating(true);

      // Convert form data to API request format
      const apiRequest = convertFormToUpdateRequest(data);

      // Use the mutation from the API hook
      updateTemplate(apiRequest, {
        onSuccess: () => {
          // On success: refetch data from backend and close dialog
          refetch();
          handleCloseDialog();
          setIsUpdating(false);
        },
        onError: (err) => {
          console.error('Failed to update template:', err);
          setIsUpdating(false);
        },
      });
    },
    [selectedTemplate, updateTemplate, handleCloseDialog, refetch]
  );

  // Handle deleting a template
  const handleDeleteTemplate = useCallback(
    (id: number) => {
      // Set loading state
      setIsDeleting(true);

      // Use the mutation from the API hook
      deleteTemplate(id, {
        onSuccess: () => {
          // On success: refetch data from backend
          refetch();
          setIsDeleting(false);
        },
        onError: (err) => {
          console.error('Failed to delete template:', err);
          setIsDeleting(false);
        },
      });
    },
    [deleteTemplate, refetch]
  );

  // Handle editing a template
  const handleEditTemplate = useCallback(
    (id: number) => {
      const template = templates.find((t) => t.id === id);
      if (template) {
        setSelectedTemplate(template);
        handleOpenDialog();
      }
    },
    [templates, handleOpenDialog]
  );

  return {
    // State
    templates,
    loading,
    error,
    openDialog,
    selectedTemplate,

    // Loading states for CRUD operations
    isCreating,
    isUpdating,
    isDeleting,

    // Handlers
    handleOpenDialog,
    handleCloseDialog,
    handleCreateTemplate,
    handleUpdateTemplate,
    handleDeleteTemplate,
    handleEditTemplate,
    handleOpenCreateDialog,

    // Refetch
    refetch: fetchTemplates,
  };
};
