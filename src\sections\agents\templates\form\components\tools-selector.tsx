import { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Checkbox,
  FormControlLabel,
  Grid,
  InputAdornment,
  Stack,
  TextField,
  Typography,
} from '@mui/material';
import { useFormContext } from 'react-hook-form';
import { Iconify } from 'src/components/iconify';
import { Tool } from 'src/services/api/tools-api';
import { TemplateFormValues } from '../template-schema';

interface ToolsSelectorProps {
  tools: Tool[];
}

export default function ToolsSelector({ tools }: ToolsSelectorProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const { watch, setValue } = useFormContext<TemplateFormValues>();
  
  const selectedToolsId = watch('toolsId') || [];

  // Filter tools based on search query
  const filteredTools = tools.filter((tool) =>
    tool.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    tool.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleToolToggle = (toolId: number) => {
    const currentTools = [...selectedToolsId];
    const toolIndex = currentTools.indexOf(toolId);

    if (toolIndex === -1) {
      // Add the tool
      currentTools.push(toolId);
    } else {
      // Remove the tool
      currentTools.splice(toolIndex, 1);
    }

    setValue('toolsId', currentTools);
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  return (
    <Stack spacing={3}>
      {/* Search Bar */}
      <TextField
        fullWidth
        value={searchQuery}
        onChange={handleSearchChange}
        placeholder="Search tools..."
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <Iconify icon="eva:search-fill" sx={{ color: 'text.disabled' }} />
            </InputAdornment>
          ),
        }}
        sx={{
          '& .MuiOutlinedInput-root': {
            borderRadius: '10px',
            background: '#FFF',
          },
        }}
      />

      {/* Tools Grid */}
      {filteredTools.length > 0 ? (
        <Grid container spacing={2}>
          {filteredTools.map((tool) => {
            const isSelected = selectedToolsId.includes(tool.id);
            
            return (
              <Grid item xs={12} sm={6} md={4} key={tool.id}>
                <Card
                  sx={{
                    cursor: 'pointer',
                    border: isSelected ? '2px solid' : '1px solid',
                    borderColor: isSelected ? 'primary.main' : 'divider',
                    bgcolor: isSelected ? 'primary.lighter' : 'background.paper',
                    transition: 'all 0.2s ease-in-out',
                    '&:hover': {
                      borderColor: 'primary.main',
                      transform: 'translateY(-2px)',
                      boxShadow: (theme) => theme.shadows[4],
                    },
                  }}
                  onClick={() => handleToolToggle(tool.id)}
                >
                  <CardContent sx={{ p: 2 }}>
                    <Stack spacing={1}>
                      <Stack direction="row" alignItems="center" spacing={1}>
                        <Checkbox
                          checked={isSelected}
                          onChange={() => handleToolToggle(tool.id)}
                          size="small"
                          sx={{ p: 0 }}
                        />
                        <Typography variant="subtitle2" noWrap>
                          {tool.name}
                        </Typography>
                      </Stack>
                      
                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical',
                          overflow: 'hidden',
                          minHeight: '2.5em',
                        }}
                      >
                        {tool.description}
                      </Typography>
                    </Stack>
                  </CardContent>
                </Card>
              </Grid>
            );
          })}
        </Grid>
      ) : (
        <Box sx={{ py: 4, textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            {searchQuery ? 'No tools found matching your search.' : 'No tools available.'}
          </Typography>
        </Box>
      )}

      {/* Selected Tools Summary */}
      {selectedToolsId.length > 0 && (
        <Box sx={{ mt: 2, p: 2, bgcolor: 'background.neutral', borderRadius: 1 }}>
          <Typography variant="subtitle2" gutterBottom>
            Selected Tools ({selectedToolsId.length})
          </Typography>
          <Stack direction="row" flexWrap="wrap" gap={1}>
            {selectedToolsId.map((toolId) => {
              const tool = tools.find((t) => t.id === toolId);
              return tool ? (
                <Box
                  key={toolId}
                  sx={{
                    px: 1.5,
                    py: 0.5,
                    bgcolor: 'primary.main',
                    color: 'primary.contrastText',
                    borderRadius: 1,
                    fontSize: '0.75rem',
                    fontWeight: 'medium',
                  }}
                >
                  {tool.name}
                </Box>
              ) : null;
            })}
          </Stack>
        </Box>
      )}
    </Stack>
  );
}
