import axiosInstance from 'src/utils/axios';
import { useApiServices } from 'src/services/hooks/use-api-services';

// Define the API endpoints for tools
export const toolEndpoints = {
  list: '/api/tools',
  details: '/api/tools',
};

// Define the Tool data type based on API response
export interface Tool {
  id: number;
  name: string;
  description: string;
  createdAt: string;
  updatedAt: string;
}

// Define the API response types
export interface ToolsListResponse {
  tools: Tool[];
  total: number;
}

// Define query parameters for list endpoint
export interface ToolsQueryParams {
  take?: number;
  skip?: number;
  name?: string;
  orderBy?: string;
  order?: 'asc' | 'desc';
}

// Create a hook to use the tools API
export const useToolsApi = () => {
  const apiServices = useApiServices({ axiosInstance });

  // Get all tools
  const useGetTools = (params?: ToolsQueryParams) => {
    return apiServices.useGetListService<ToolsListResponse, ToolsQueryParams>({
      url: toolEndpoints.list,
      params,
    });
  };

  // Get a single tool by ID
  const useGetTool = (id: number) => {
    return apiServices.useGetItemService<Tool>({
      url: toolEndpoints.details,
      id: id.toString(),
    });
  };

  return {
    useGetTools,
    useGetTool,
  };
};
