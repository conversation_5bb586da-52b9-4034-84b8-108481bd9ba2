import { useState, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Stack, Typography, Box, CircularProgress } from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { AppTable } from 'src/components/table/app-table/app-table';
import { paths } from 'src/routes/paths';
import { AppButton, AppContainer } from 'src/components/common';
import ConfirmDialog from 'src/components/custom-dialog/confirm-dialog';
import { useTemplatesApi, Template } from 'src/services/api/use-templates-api';
import { AppTablePropsType } from 'src/components/table';
import { Label } from 'src/components/label';
import LongMenu from 'src/components/long-menu';
import useTable from 'src/components/table/use-table';
import AgentForm from '../form/agent-form';
import { AgentFormValues } from '../form/use-agent-form';

// ----------------------------------------------------------------------

export const AgentsView = () => {
  const { t } = useTranslation();
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
  const [selectedId, setSelectedId] = useState<number | null>(null);
  const table = useTable();

  // Get templates data from API
  const { useGetTemplates, useDeleteTemplate } = useTemplatesApi();
  const { data: templatesResponse, isLoading, isError, refetch } = useGetTemplates();
  const { mutate: deleteTemplate, isPending } = useDeleteTemplate();

  const templates = templatesResponse?.templates || [];
  const loading = isLoading;
  const error = isError ? 'Failed to load templates' : null;

  // Dialog handlers
  const handleOpenDialog = useCallback(() => {
    setOpenDialog(true);
  }, []);

  const handleCloseDialog = useCallback(() => {
    setOpenDialog(false);
    setSelectedTemplate(null);
  }, []);

  const handleOpenCreateDialog = useCallback(() => {
    setSelectedTemplate(null);
    handleOpenDialog();
  }, [handleOpenDialog]);

  // Handle form submission
  const handleFormSubmit = useCallback(
    (data: AgentFormValues) => {
      // Form submission is handled in the form itself
      handleCloseDialog();
      refetch(); // Refetch data after submission
    },
    [handleCloseDialog, refetch]
  );

  // Handle editing a template
  const handleEditTemplate = useCallback(
    (id: number) => {
      const template = templates.find((t) => t.id === id);
      if (template) {
        setSelectedTemplate(template);
        handleOpenDialog();
      }
    },
    [templates, handleOpenDialog]
  );

  // Convert Template to AgentForm format
  const convertTemplateToAgentFormat = useCallback((template: Template | null) => {
    if (!template) return null;

    return {
      id: template.id,
      creatorId: template.creatorId,
      name: template.name,
      description: template.description,
      categoryId: template.categoryId,
      type: template.type,
      model: template.model,
      createdAt: template.createdAt,
      updatedAt: template.updatedAt,
      category: template.category,
    };
  }, []);

  // Handle opening the confirm dialog for deleting templates
  const handleOpenConfirmDialog = useCallback((id: number) => {
    setSelectedId(id);
    setOpenConfirmDialog(true);
  }, []);

  // Handle closing the confirm dialog
  const handleCloseConfirmDialog = useCallback(() => {
    setOpenConfirmDialog(false);
    setSelectedId(null);
  }, []);

  // Handle confirming delete
  const handleConfirmDelete = useCallback(() => {
    if (selectedId) {
      deleteTemplate(selectedId, {
        onSuccess: () => {
          refetch();
          handleCloseConfirmDialog();
        },
        onError: (error) => {
          console.error('Failed to delete template:', error);
          handleCloseConfirmDialog();
        },
      });
    }
  }, [selectedId, deleteTemplate, refetch, handleCloseConfirmDialog]);

  // Menu options for each template row
  const MENU_OPTIONS = useCallback(
    (template: Template) => [
      {
        label: 'Edit',
        icon: 'solar:pen-bold',
        onClick: () => handleEditTemplate(template.id),
      },
      {
        label: 'Delete',
        icon: 'solar:trash-bin-trash-bold',
        onClick: () => handleOpenConfirmDialog(template.id),
        sx: { color: 'error.main' },
      },
    ],
    [handleEditTemplate, handleOpenConfirmDialog]
  );

  // Table columns configuration
  const columns: AppTablePropsType<Template>['columns'] = useMemo(
    () => [
      {
        name: 'name',
        PreviewComponent: (data) => {
          const { name, description } = data;
          return (
            <Stack spacing={0.5} sx={{ maxWidth: 280 }}>
              <Typography variant="subtitle2" noWrap>
                {name}
              </Typography>
              <Typography variant="caption" sx={{ color: 'text.disabled' }} noWrap>
                {description}
              </Typography>
            </Stack>
          );
        },
      },
      {
        name: 'category',
        PreviewComponent: (data) => {
          const { category } = data;
          return (
            <Label
              variant="soft"
              sx={{
                bgcolor: `${category.theme}20`,
                color: category.theme,
                fontWeight: 'medium',
              }}
            >
              {category.name}
            </Label>
          );
        },
      },
      {
        name: 'type',
        PreviewComponent: (data) => {
          const { type } = data;
          return (
            <Label variant="soft" color={type === 'SINGLE' ? 'info' : 'warning'}>
              {type === 'SINGLE' ? 'Single Agent' : 'Multi Agent'}
            </Label>
          );
        },
      },
      {
        name: 'model',
        PreviewComponent: (data) => {
          const { model } = data;
          const modelLabel =
            model === 'GPT_4O_MINI'
              ? 'GPT-4o Mini'
              : model === 'GPT_4O'
                ? 'GPT-4o'
                : 'GPT-3.5 Turbo';
          return (
            <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
              {modelLabel}
            </Typography>
          );
        },
      },
      {
        name: 'id',
        cellSx: { width: '50px' },
        PreviewComponent: (row) => {
          return <LongMenu options={MENU_OPTIONS(row)} />;
        },
      },
    ],
    [MENU_OPTIONS]
  );

  return (
    <AppContainer
      title="Templates"
      pageTitle="Templates"
      routeLinks={[
        {
          name: 'Dashboard',
          href: paths.dashboard.root,
        },
        {
          name: 'Templates',
        },
      ]}
      buttons={[
        {
          label: 'Create Template',
          variant: 'outlined',
          startIcon: <Iconify icon="majesticons:plus" />,
          onClick: handleOpenCreateDialog,
        },
      ]}
    >
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 10 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Box sx={{ py: 10, textAlign: 'center' }}>
          <Typography variant="h6" color="error" paragraph>
            {error}
          </Typography>
          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
            Please try again later
          </Typography>
        </Box>
      ) : (
        <Box sx={{ mt: 3 }}>
          <AppTable<Template>
            headLabels={[
              { id: 'name', label: 'Template' },
              { id: 'category', label: 'Category' },
              { id: 'type', label: 'Type' },
              { id: 'model', label: 'Model' },
              { id: 'actions', label: '' },
            ]}
            dataCount={templates.length}
            data={templates}
            columns={columns}
            table={table}
            noDataLabel="No templates found"
          />
        </Box>
      )}

      {/* Template Dialog */}
      <AgentForm
        open={openDialog}
        onClose={handleCloseDialog}
        agent={convertTemplateToAgentFormat(selectedTemplate)}
        onSubmit={handleFormSubmit}
      />

      {/* Confirm Delete Dialog */}
      <ConfirmDialog
        open={openConfirmDialog}
        onClose={handleCloseConfirmDialog}
        title="Delete Template"
        content="Are you sure you want to delete this template? This action cannot be undone."
        action={
          <Box sx={{ display: 'flex', gap: 1 }}>
            <AppButton label="Cancel" variant="outlined" onClick={handleCloseConfirmDialog} />
            <AppButton
              isLoading={isPending}
              label="Delete"
              variant="contained"
              color="error"
              onClick={handleConfirmDelete}
            />
          </Box>
        }
      />
    </AppContainer>
  );
};
