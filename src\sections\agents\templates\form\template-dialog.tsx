import { Dialog, DialogContent, IconButton, Typography, Box, Stack } from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { Template } from 'src/services/api/templates-api';
import TemplateForm from './template-form';
import { TemplateFormValues } from './template-schema';

interface TemplateDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: TemplateFormValues) => void;
  defaultValues?: Partial<Template>;
  loading?: boolean;
}

export default function TemplateDialog({
  open,
  onClose,
  onSubmit,
  defaultValues,
  loading = false,
}: TemplateDialogProps) {
  const isEditing = !!defaultValues;

  // Convert Template to TemplateFormValues if editing
  const formDefaultValues: Partial<TemplateFormValues> | undefined = defaultValues
    ? {
        name: defaultValues.name,
        description: defaultValues.description,
        systemMessage: defaultValues.systemMessage,
        type: defaultValues.type,
        categoryId: defaultValues.categoryId,
        model: defaultValues.model,
        toolsId: defaultValues.toolsId,
      }
    : undefined;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          minHeight: '70vh',
        },
      }}
    >
      {/* Dialog Header */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          p: 3,
          pb: 0,
        }}
      >
        <Stack spacing={0.5}>
          <Typography variant="h5">
            {isEditing ? 'Edit Template' : 'Create New Template'}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {isEditing
              ? 'Update the template information and settings'
              : 'Fill in the details to create a new template'}
          </Typography>
        </Stack>

        <IconButton
          onClick={onClose}
          sx={{
            color: 'text.secondary',
            '&:hover': {
              bgcolor: 'action.hover',
            },
          }}
        >
          <Iconify icon="mingcute:close-line" />
        </IconButton>
      </Box>

      {/* Dialog Content */}
      <DialogContent sx={{ p: 0 }}>
        <TemplateForm
          onSubmit={onSubmit}
          onCancel={onClose}
          defaultValues={formDefaultValues}
          loading={loading}
        />
      </DialogContent>
    </Dialog>
  );
}
