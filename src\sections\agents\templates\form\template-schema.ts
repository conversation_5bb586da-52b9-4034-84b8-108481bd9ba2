import * as z from 'zod';

// Define the available model options as constants
export const MODEL_OPTIONS = [
  { value: 'GPT_4O_MINI', label: 'GPT-4o Mini' },
  { value: 'GPT_4O', label: 'GPT-4o' },
  { value: 'GPT_3_5_TURBO', label: 'GPT-3.5 Turbo' },
] as const;

// Define the available type options as constants
export const TYPE_OPTIONS = [
  { value: 'SINGLE', label: 'Single Agent' },
  { value: 'MULTI', label: 'Multi Agent' },
] as const;

export const templateSchema = z.object({
  name: z.string().min(1, 'Template name is required'),
  description: z.string().min(1, 'Description is required'),
  systemMessage: z.string().min(1, 'System message is required'),
  type: z.enum(['SINGLE', 'MULTI'], {
    required_error: 'Type is required',
  }),
  categoryId: z.number({
    required_error: 'Category is required',
  }).min(1, 'Category is required'),
  model: z.enum(['GPT_4O_MINI', 'GPT_4O', 'GPT_3_5_TURBO'], {
    required_error: 'Model is required',
  }),
  toolsId: z.array(z.number()).default([]),
});

export type TemplateFormValues = z.infer<typeof templateSchema>;
