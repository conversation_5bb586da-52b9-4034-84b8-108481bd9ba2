import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Stack, Box, Typography, Stepper, Step, StepLabel, StepContent } from '@mui/material';
import { AppButton } from 'src/components/common';
import { Form } from 'src/components/hook-form/form-provider';
import { Field } from 'src/components/hook-form/fields';
import { templateSchema, TemplateFormValues, MODEL_OPTIONS, TYPE_OPTIONS } from './template-schema';
import { useCategoriesApi } from 'src/services/api/categories-api';
import { useToolsApi } from 'src/services/api/tools-api';
import { useState } from 'react';
import ToolsSelector from './components/tools-selector';

interface TemplateFormProps {
  onSubmit: (data: TemplateFormValues) => void;
  onCancel: () => void;
  defaultValues?: Partial<TemplateFormValues>;
  loading?: boolean;
}

const FORM_STEPS = [
  {
    label: 'Basic Information',
    description: 'Enter template name, description, and system message',
  },
  {
    label: 'Configuration',
    description: 'Select category, type, and model',
  },
  {
    label: 'Tools Selection',
    description: 'Choose tools for this template',
  },
];

export default function TemplateForm({
  onSubmit,
  onCancel,
  defaultValues,
  loading = false,
}: TemplateFormProps) {
  const [activeStep, setActiveStep] = useState(0);

  // Get categories and tools data
  const { useGetCategories } = useCategoriesApi();
  const { useGetTools } = useToolsApi();
  const { data: categoriesResponse } = useGetCategories();
  const { data: toolsResponse } = useGetTools();

  const categories = categoriesResponse?.categories || [];
  const tools = toolsResponse?.tools || [];

  const methods = useForm<TemplateFormValues>({
    mode: 'onChange',
    resolver: zodResolver(templateSchema),
    defaultValues: {
      name: '',
      description: '',
      systemMessage: '',
      type: 'SINGLE',
      categoryId: 0,
      model: 'GPT_4O_MINI',
      toolsId: [],
      ...defaultValues,
    },
  });

  const {
    handleSubmit,
    trigger,
    formState: { errors, isSubmitting },
    watch,
  } = methods;

  const selectedTools = watch('toolsId');

  const handleNext = async () => {
    let fieldsToValidate: (keyof TemplateFormValues)[] = [];
    
    if (activeStep === 0) {
      fieldsToValidate = ['name', 'description', 'systemMessage'];
    } else if (activeStep === 1) {
      fieldsToValidate = ['type', 'categoryId', 'model'];
    }

    const isStepValid = await trigger(fieldsToValidate);
    if (isStepValid) {
      setActiveStep((prev) => prev + 1);
    }
  };

  const handleBack = () => {
    setActiveStep((prev) => prev - 1);
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Stack spacing={3}>
            <Field.Text
              name="name"
              label="Template Name"
              placeholder="Enter template name"
              InputLabelProps={{ shrink: true }}
            />
            <Field.Text
              name="description"
              label="Description"
              placeholder="Enter template description"
              multiline
              rows={3}
              InputLabelProps={{ shrink: true }}
            />
            <Field.Text
              name="systemMessage"
              label="System Message"
              placeholder="Enter system message for the template"
              multiline
              rows={4}
              InputLabelProps={{ shrink: true }}
            />
          </Stack>
        );

      case 1:
        return (
          <Stack spacing={3}>
            <Field.Select
              name="categoryId"
              label="Category"
              placeholder="Select a category"
              InputLabelProps={{ shrink: true }}
            >
              <option value={0} disabled>Select a category</option>
              {categories.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </Field.Select>

            <Field.Select
              name="type"
              label="Type"
              placeholder="Select template type"
              InputLabelProps={{ shrink: true }}
            >
              {TYPE_OPTIONS.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </Field.Select>

            <Field.Select
              name="model"
              label="Model"
              placeholder="Select AI model"
              InputLabelProps={{ shrink: true }}
            >
              {MODEL_OPTIONS.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </Field.Select>
          </Stack>
        );

      case 2:
        return (
          <Stack spacing={3}>
            <Typography variant="subtitle2">
              Select Tools ({selectedTools.length} selected)
            </Typography>
            <ToolsSelector tools={tools} />
          </Stack>
        );

      default:
        return null;
    }
  };

  return (
    <Form methods={methods} onSubmit={handleSubmit(onSubmit)}>
      <Box sx={{ p: 3 }}>
        <Stepper activeStep={activeStep} orientation="vertical">
          {FORM_STEPS.map((step, index) => (
            <Step key={step.label}>
              <StepLabel>
                <Typography variant="subtitle1">{step.label}</Typography>
                <Typography variant="body2" color="text.secondary">
                  {step.description}
                </Typography>
              </StepLabel>
              <StepContent>
                <Box sx={{ py: 2 }}>
                  {renderStepContent(index)}
                </Box>
                <Box sx={{ mb: 2 }}>
                  <Stack direction="row" spacing={1} justifyContent="flex-end">
                    {index > 0 && (
                      <AppButton
                        variant="outlined"
                        onClick={handleBack}
                        disabled={loading}
                      >
                        Back
                      </AppButton>
                    )}
                    {index < FORM_STEPS.length - 1 ? (
                      <AppButton
                        variant="contained"
                        onClick={handleNext}
                        disabled={loading}
                      >
                        Next
                      </AppButton>
                    ) : (
                      <Stack direction="row" spacing={1}>
                        <AppButton
                          variant="outlined"
                          onClick={onCancel}
                          disabled={loading}
                        >
                          Cancel
                        </AppButton>
                        <AppButton
                          type="submit"
                          variant="contained"
                          loading={loading || isSubmitting}
                        >
                          {defaultValues ? 'Update Template' : 'Create Template'}
                        </AppButton>
                      </Stack>
                    )}
                  </Stack>
                </Box>
              </StepContent>
            </Step>
          ))}
        </Stepper>
      </Box>
    </Form>
  );
}
