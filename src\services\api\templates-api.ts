import axiosInstance from 'src/utils/axios';
import { useApiServices } from 'src/services/hooks/use-api-services';

// Define the API endpoints for templates
export const templateEndpoints = {
  list: '/api/templates',
  details: '/api/templates',
};

// Define the Template data type based on API response
export interface Template {
  id: number;
  name: string;
  description: string;
  systemMessage: string;
  type: 'SINGLE' | 'MULTI';
  categoryId: number;
  model: 'GPT_4O_MINI' | 'GPT_4O' | 'GPT_3_5_TURBO';
  toolsId: number[];
  createdAt: string;
  updatedAt: string;
  // Additional properties for UI compatibility
  category?: {
    id: number;
    name: string;
    theme: string;
    icon: string;
  };
  tools?: {
    id: number;
    name: string;
    description: string;
  }[];
}

// Define the API response types
export interface TemplatesListResponse {
  categories: Template[];
  total: number;
}

// Define the request body types
export interface CreateTemplateRequest {
  name: string;
  description: string;
  systemMessage: string;
  type: 'SINGLE' | 'MULTI';
  categoryId: number;
  model: 'GPT_4O_MINI' | 'GPT_4O' | 'GPT_3_5_TURBO';
  toolsId: number[];
}

export interface UpdateTemplateRequest {
  name: string;
  description: string;
  systemMessage: string;
  type: 'SINGLE' | 'MULTI';
  categoryId: number;
  model: 'GPT_4O_MINI' | 'GPT_4O' | 'GPT_3_5_TURBO';
  toolsId: number[];
}

// Define query parameters for list endpoint
export interface TemplatesQueryParams {
  take?: number;
  skip?: number;
  name?: string;
  orderBy?: string;
  order?: 'asc' | 'desc';
}

// Create a hook to use the templates API
export const useTemplatesApi = () => {
  const apiServices = useApiServices({ axiosInstance });

  // Get all templates
  const useGetTemplates = (params?: TemplatesQueryParams) => {
    return apiServices.useGetListService<TemplatesListResponse, TemplatesQueryParams>({
      url: templateEndpoints.list,
      params,
    });
  };

  // Get a single template by ID
  const useGetTemplate = (id: number) => {
    return apiServices.useGetItemService<Template>({
      url: templateEndpoints.details,
      id: id.toString(),
    });
  };

  // Create a new template
  const useCreateTemplate = (onSuccess?: (data: Template) => void) => {
    return apiServices.usePostService<CreateTemplateRequest, Template>({
      url: templateEndpoints.list,
      onSuccess,
      withFormData: false,
    });
  };

  // Update a template using PATCH
  const useUpdateTemplate = (id: number, onSuccess?: () => void) => {
    return apiServices.usePatchService<UpdateTemplateRequest>({
      url: templateEndpoints.details,
      id: id.toString(),
      onSuccess,
      withFormData: false,
      queryKey: templateEndpoints.list + 'list',
    });
  };

  // Delete a template
  const useDeleteTemplate = (onSuccess?: () => void) => {
    return apiServices.useDeleteService<number>({
      url: templateEndpoints.details,
      urlAfterSuccess: templateEndpoints.list + 'list',
      onSuccess,
    });
  };

  return {
    useGetTemplates,
    useGetTemplate,
    useCreateTemplate,
    useUpdateTemplate,
    useDeleteTemplate,
  };
};
